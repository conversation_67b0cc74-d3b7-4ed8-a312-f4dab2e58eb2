#!/usr/bin/env python3
"""
Test script to verify CLI arguments are passed correctly to prompt mode.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_prompt_mode_defaults():
    """Test that CLI arguments are correctly used as defaults in prompt mode."""
    
    # Simulate the logic from main() for prompt mode
    class MockArgs:
        def __init__(self):
            self.input_files = []
            self.output_dir = None
            self.model = 'large'  # CLI argument
            self.language = 'norwegian'  # CLI argument
            self.temperature = 0.3
            self.prompt = True
    
    args = MockArgs()
    working_directory = os.getcwd()
    
    # Replicate the logic from lines 1005-1010
    default_input_files = args.input_files if args.input_files else [os.path.join(working_directory, "audioclip.mp3")]
    default_output_dir = args.output_dir if args.output_dir else os.path.join(working_directory, "output")
    default_model = args.model if args.model else "medium"
    valid_languages = ['english', 'norwegian', 'french', 'spanish', 'german']
    default_language = args.language if args.language else "english"
    default_temperature = str(args.temperature) if args.temperature is not None else "0.0"
    
    print("=== Prompt Mode Default Resolution Test ===")
    print(f"CLI args: model='{args.model}', language='{args.language}', temperature={args.temperature}")
    print(f"Resolved defaults for Rich prompts:")
    print(f"  default_model: '{default_model}'")
    print(f"  default_language: '{default_language}'")
    print(f"  default_temperature: '{default_temperature}'")
    print(f"  default_output_dir: '{default_output_dir}'")
    
    # Verify the logic works correctly
    assert default_model == 'large', f"Expected 'large', got '{default_model}'"
    assert default_language == 'norwegian', f"Expected 'norwegian', got '{default_language}'"
    assert default_temperature == '0.3', f"Expected '0.3', got '{default_temperature}'"
    
    print("\n✅ All assertions passed! CLI arguments are correctly used as defaults.")

if __name__ == "__main__":
    test_prompt_mode_defaults()
