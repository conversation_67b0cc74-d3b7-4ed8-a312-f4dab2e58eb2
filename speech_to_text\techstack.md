# Technology Stack

## Core Framework
- **Python 3.9+** - Primary language (optimized for 3.13)
- **OpenAI Whisper 20250625** - Latest speech-to-text transcription engine
- **PyTorch 2.7.1** - Latest ML framework

## Key Dependencies
- **openai-whisper** - Audio transcription (latest stable)
- **librosa** - Modern audio processing (replaces pydub)
- **soundfile** - Audio I/O operations
- **numpy** - Numerical operations
- **rich** - Terminal UI/formatting
- **loguru** - Enhanced logging
- **tqdm** - Progress bars
- **PyYAML** - Configuration parsing
- **transformers** - Hugging Face transformers
- **tiktoken** - OpenAI tokenization

## Development Tools
- **uv** - Modern Python package manager (replaces pip)
- **virtual environment** - Isolated dependencies
- **batch scripts** - Windows automation

## Project Structure
- Simplified single-module architecture (`main.py`)
- YAML-based configuration
- Batch processing support
- Interactive CLI interface
- UV-based dependency management
