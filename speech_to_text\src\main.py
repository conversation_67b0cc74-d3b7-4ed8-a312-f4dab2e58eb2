#!/usr/bin/env python3

import sys

# ---------------------------------------------------------------------
# Force stdin/stdout/stderr to UTF-8, especially on Windows,
# to handle Norwegian characters (æ, ø, å) properly.
# ---------------------------------------------------------------------
try:
    sys.stdin.reconfigure(encoding='utf-8')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')
except AttributeError:
    pass

import argparse
import json
import logging
import os
import platform
import shlex
import socket
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from enum import Enum
from pathlib import Path

import numpy as np
import whisper
from loguru import logger
import librosa
import soundfile as sf
from rich import box
from rich.console import Console
from rich.logging import RichHandler
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.theme import Theme
from tqdm import tqdm


# LOGGER CONFIGURATION
# =======================================================

class LoggingLevel(Enum):
    """
    Enum for log levels.
    """
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    WARNING = "WARNING"
    CRITICAL = "CRITICAL"


class LoggerConfiguration:
    """
    Configuration class for setting up and customizing the logger.
    """
    DEFAULT_VERBOSITY = "normal"
    DEFAULT_TIME_FORMAT = "[%X]"
    DEFAULT_THEME = {
        "logging.level.trace": "dim #b4009e",
        "logging.level.debug": "#bf00ff",
        "logging.level.info": "#3b78ff",
        "logging.level.success": "#12a50a",
        "logging.level.error": "#9b1616",
        "logging.level.warning": "#c0c005",
        "logging.level.critical": "black on bright_red",
        "log.time": "dim white",
        "traceback.border": "#5f0810",
    }

    VERBOSITY_LEVELS = {
        "quiet": LoggingLevel.WARNING.value,
        "normal": LoggingLevel.INFO.value,
        "verbose": LoggingLevel.DEBUG.value,
    }

    def __init__(
        self,
        verbosity: str = DEFAULT_VERBOSITY,
        use_custom_theme: bool = True,
        custom_theme: dict = None,
        enable_rich_tracebacks: bool = True,
        traceback_extra_lines: int = 3,
        show_local_vars_in_traceback: bool = False,
        display_time: bool = True,
        display_log_level: bool = True,
        time_format: str = DEFAULT_TIME_FORMAT,
        log_to_file: bool = False,
        log_directory: str = "logs",
        log_to_current_directory: bool = False
    ):
        self.verbosity = verbosity
        self.use_custom_theme = use_custom_theme
        self.custom_theme = custom_theme if custom_theme else self.DEFAULT_THEME
        self.enable_rich_tracebacks = enable_rich_tracebacks
        self.traceback_extra_lines = traceback_extra_lines
        self.show_local_vars_in_traceback = show_local_vars_in_traceback
        self.display_time = display_time
        self.display_log_level = display_log_level
        self.time_format = time_format
        self.log_to_file = log_to_file
        self.log_directory = log_directory
        self.log_to_current_directory = log_to_current_directory

    def setup_logger(self) -> None:
        """
        Sets up the logger based on the specified configuration.
        """
        log_level = self.VERBOSITY_LEVELS.get(self.verbosity, LoggingLevel.INFO.value)
        self._remove_existing_loggers()
        self._setup_standard_logging(log_level)
        self._setup_rich_loguru_handler(log_level)
        if self.log_to_file:
            self._setup_file_logging(log_level)

    def _remove_existing_loggers(self) -> None:
        """
        Removes any existing logger handlers.
        """
        logger.remove()

    def _setup_standard_logging(self, log_level: str) -> None:
        """
        Configures the standard Python logging to use Loguru logger.
        """
        logging.basicConfig(handlers=[self.LoguruRedirectHandler()], level=log_level)
        logging.getLogger().handlers = [self.LoguruRedirectHandler()]

    def _setup_rich_loguru_handler(self, log_level: str) -> None:
        """
        Configures Loguru logger with Rich handler for enhanced logging output.
        """
        logger.add(
            RichHandler(
                console=Console(theme=self._set_logging_theme(self.use_custom_theme)),
                rich_tracebacks=self.enable_rich_tracebacks,
                tracebacks_extra_lines=self.traceback_extra_lines,
                tracebacks_show_locals=self.show_local_vars_in_traceback,
                show_time=self.display_time,
                show_level=self.display_log_level,
                enable_link_path=True,
            ),
            format="{message}",
            level=log_level
        )

    def _setup_file_logging(self, log_level: str) -> None:
        """
        Configures Loguru logger to also log to a file.
        """
        if self.log_to_current_directory:
            log_directory = Path.cwd() / "logs"
        else:
            script_dir = Path(__file__).resolve().parent
            log_directory = script_dir / self.log_directory

        log_directory.mkdir(parents=True, exist_ok=True)
        computer_name = socket.gethostname()
        script_name = Path(__file__).stem
        log_file_path = log_directory / f"{computer_name}_{script_name}.log"

        logger.add(
            log_file_path,
            rotation="1 week",
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
            enqueue=True,
            serialize=False,
            mode="w",
        )

    def _set_logging_theme(self, use_override: bool) -> Theme:
        """
        Defines the theme for the logger.
        """
        return Theme(self.custom_theme) if use_override else Theme()

    class LoguruRedirectHandler(logging.Handler):
        """
        Intercepts standard logging messages and redirects them to loguru.
        """
        def emit(self, record):
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno

            frame, depth = logging.currentframe(), 2
            while frame and frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


# CONFIG LOADING
# =======================================================

def load_config(file_path):
    """
    Load configuration from a JSON or YAML file.
    If the file includes a 'files' list, it can be used for batch processing.
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        if file_path.endswith('.json'):
            return json.load(file)
        elif file_path.endswith('.yaml') or file_path.endswith('.yml'):
            return yaml.safe_load(file)
        else:
            raise ValueError("Unsupported configuration file format. Use .json or .yaml")


# AUDIO TRANSCRIPTION
# =======================================================

def convert_to_wav(input_file):
    """
    Convert audio file to WAV format using librosa and soundfile.
    """
    try:
        # Load audio file with librosa
        audio_data, sample_rate = librosa.load(input_file, sr=None)
        wav_file = input_file.rsplit('.', 1)[0] + '.wav'

        # Save as WAV using soundfile
        sf.write(wav_file, audio_data, sample_rate)
        return wav_file
    except Exception as e:
        logger.error(f"Error converting {input_file} to WAV: {e}")
        return input_file

def transcribe_audio(input_file, model_name, language=None, temperature=0.0, segment_duration=30):
    """
    Transcribe the given audio file using the specified Whisper model.
    Splits the audio into segments (default: 30 seconds) for incremental transcription.
    """
    model = whisper.load_model(model_name)
    if not model:
        return None

    was_converted = False
    # Convert audio to WAV format if it is not already .wav
    if not input_file.endswith('.wav'):
        input_file = convert_to_wav(input_file)
        was_converted = True

    try:
        # Load audio
        audio = whisper.load_audio(input_file)
        audio_length = len(audio) / whisper.audio.SAMPLE_RATE

        # Segment audio
        segments = np.array_split(audio, max(1, int(audio_length / segment_duration)))

        transcription = ""

        # Create a tqdm progress bar
        with tqdm(total=audio_length, unit='s', desc="Transcribing") as pbar:
            for segment in segments:
                segment_result = model.transcribe(
                    segment, language=language, temperature=temperature
                )
                transcription += segment_result['text']
                pbar.update(len(segment) / whisper.audio.SAMPLE_RATE)

        # Remove the temporary WAV file if it was newly created
        if was_converted and os.path.exists(input_file):
            os.remove(input_file)

        return transcription
    except Exception as e:
        logger.error(f"Error during transcription of {input_file}: {e}")
        return None


# MAIN SCRIPT / CLI
# =======================================================

console = Console()
logger_config = LoggerConfiguration(
    verbosity="quiet",
    use_custom_theme=True,
    enable_rich_tracebacks=True,
    traceback_extra_lines=3,
    show_local_vars_in_traceback=False,
    display_time=True,
    display_log_level=True,
    time_format="[%X]",
    log_to_file=True
)
logger_config.setup_logger()

def parse_arguments():
    """
    Parse command-line arguments with integrated prompting for missing values,
    and detect whether we should do batch mode from config (if 'files' key is present).
    """
    valid_models = [
        'tiny', 'base', 'small', 'medium', 'large',
        'tiny.en', 'base.en', 'small.en', 'medium.en',
        'large-v1', 'large-v2', 'large-v3'
    ]
    valid_languages = ['english', 'norwegian', 'french', 'spanish', 'german']
    parser = argparse.ArgumentParser(description="Transcribe audio/video files to text using Whisper.")
    parser.add_argument('input_files', type=str, nargs='*', help="Paths to the input files")
    parser.add_argument('-o', '--output_dir', type=str, help="Directory to save the transcribed texts")
    parser.add_argument('--model', type=str, default='medium', choices=valid_models,
                        help="Whisper model to use (tiny, base, small, medium, large, etc.)")
    parser.add_argument('--config', type=str, help="Path to configuration file (JSON or YAML)")
    parser.add_argument('--prompt', action='store_true', help="Prompt the user for input values")
    parser.add_argument('--debug', action='store_true', help="Enable debug mode for more verbose output")
    parser.add_argument('--language', type=str, choices=valid_languages, help="Language of the audio")
    parser.add_argument('--temperature', type=float, default=0.0, help="Temperature for transcription")
    parser.add_argument('--overwrite', action='store_true', help="Overwrite existing files instead of skipping (default: False)")
    parser.add_argument('--backup', action='store_true', help="Create backup with auto-incremented filename instead of skipping (default: False)")
    args = parser.parse_args()

    # Validate mutually exclusive options
    if args.overwrite and args.backup:
        console.print("Error: --overwrite and --backup options are mutually exclusive", style="bold red")
        sys.exit(1)

    # Default to single-file mode
    args.batch_mode = False
    args.batch_files = []

    # If a config file is provided, try loading it
    if args.config:
        config = load_config(args.config)

        # If 'files' key exists in the config, treat it as a batch scenario
        if 'files' in config:
            args.batch_mode = True
            args.batch_files = config['files']
            # Pull global-level config settings if provided
            args.output_dir = config.get('output_dir', args.output_dir)
            args.model = config.get('model', args.model)
            args.language = config.get('language', args.language)
            args.temperature = config.get('temperature', args.temperature)
        else:
            # Otherwise, proceed with single-file approach but override defaults if present
            args.input_files = config.get('input_files', args.input_files)
            args.output_dir = config.get('output_dir', args.output_dir)
            args.model = config.get('model', args.model)
            args.language = config.get('language', args.language)
            args.temperature = config.get('temperature', args.temperature)

    return args

def clear_console():
    """
    Clears the terminal screen for Windows, Linux, or macOS.
    """
    if platform.system() == "Windows":
        os.system("cls")
    else:
        os.system("clear")

def ensure_directory_exists(directory: Path):
    """
    Ensure the output directory exists or create it.
    """
    directory.mkdir(parents=True, exist_ok=True)
    logger.info(f"Created (or found existing) output directory: {directory}")

def display_summary(args, transcription):
    """
    Display a summary of the configuration settings and partial transcription in a Rich table.
    """
    table = Table(
        title="Configuration Summary",
        show_header=True,
        header_style="bold magenta",
        box=box.ASCII
    )
    table.add_column("Parameter", style="dim", width=20)
    table.add_column("Value", style="bold cyan")

    # Show either the batch file list or the single input files
    if args.batch_mode:
        file_list = [item['file_path'] for item in args.batch_files]
        table.add_row("Input Files (Batch)", ", ".join(file_list))
    else:
        table.add_row("Input Files", ", ".join(args.input_files) if args.input_files else "None")

    table.add_row("Output Directory", args.output_dir or "Not specified")
    table.add_row("Model", args.model or "Not specified")

    # Add file handling mode
    if hasattr(args, 'overwrite') and args.overwrite:
        file_handling = "Overwrite existing files"
    elif hasattr(args, 'backup') and args.backup:
        file_handling = "Backup existing files"
    else:
        file_handling = "Skip existing files"
    table.add_row("File Handling", file_handling)

    snippet = transcription[:100] + "..." if len(transcription) > 100 else transcription
    table.add_row("Transcription", snippet)
    console.print(table)
    logger.info("Displayed configuration summary.")

def resolve_paths(input_files, working_directory):
    """
    Resolve relative file paths to absolute paths based on the working directory.
    Also strips quotes if provided in the CLI.
    """
    resolved_files = []
    for file in input_files:
        file = file.strip('\"\'')
        path = Path(file)
        if not path.is_absolute():
            path = working_directory / path
        resolved_files.append(str(path))
    return resolved_files


def get_backup_filename(file_path: Path) -> Path:
    """Generate a backup filename with auto-incremented suffix."""
    counter = 1
    while True:
        backup_path = file_path.with_suffix(f"{file_path.suffix}.{counter:03d}")
        if not backup_path.exists():
            return backup_path
        counter += 1


def transcribe_and_save(input_file, output_dir, model, language, temperature, overwrite=False, backup=False):
    """
    Transcribe a single file and save the resulting text to the specified output directory,
    using UTF-8 to accommodate all possible Unicode characters.

    Now also SKIPS if a .txt with the same name already exists in output_dir.
    """
    path_obj = Path(input_file)
    output_file = Path(output_dir) / (path_obj.stem + '.txt')

    # Handle existing files based on parameters
    if output_file.exists():
        if backup:
            # Create backup with auto-incremented filename
            backup_path = get_backup_filename(output_file)
            output_file.rename(backup_path)
            console.print(f"Backed up existing file to: [bold cyan]{backup_path}[/bold cyan]")
            logger.info(f"Backed up existing file {output_file} to {backup_path}")
        elif not overwrite:
            # Skip if not overwriting and not backing up
            console.print(f"Skipped transcription (file already exists): [bold cyan]{output_file}[/bold cyan]")
            logger.info(f"Skipped transcription for {input_file} - {output_file} already exists.")
            return
        else:
            # Overwrite mode - just log that we're overwriting
            console.print(f"Overwriting existing file: [bold cyan]{output_file}[/bold cyan]")
            logger.info(f"Overwriting existing file {output_file}")

    if not path_obj.is_file():
        console.print(f"The specified input file does not exist:\n{input_file}", style="bold red")
        logger.error(f"The specified input file does not exist: {input_file}. Skipping.")
        return

    start_time = datetime.now()
    transcription = transcribe_audio(str(path_obj), model, language, temperature)
    if transcription is None:
        logger.error(f"Transcription returned None for file: {input_file}")
        return

    ensure_directory_exists(output_file.parent)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(transcription)

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    console.print(f"Transcription saved to: [bold cyan]{output_file}[/bold cyan] (Duration: {duration:.2f}s)")
    logger.info(f"Transcription saved to: {output_file} (Duration: {duration:.2f}s)")
    return output_file

def interactive_session():
    """Interactive CLI session similar to SSH Key Manager."""
    working_directory = Path.cwd()

    while True:
        clear_console()
        display_header()

        print("\n===== Speech-to-Text Transcription Tool =====")
        print("What would you like to do?")
        print("1. Transcribe single audio file")
        print("2. Batch transcribe multiple files")
        print("3. Use configuration file")
        print("4. Download Whisper models")
        print("5. View transcription history")
        print("6. Settings & preferences")
        print("7. Exit")

        choice = input("\n> ").strip()

        if choice == "1":
            single_file_transcription(working_directory)

        elif choice == "2":
            batch_transcription(working_directory)

        elif choice == "3":
            config_file_transcription(working_directory)

        elif choice == "4":
            download_models()

        elif choice == "5":
            view_history(working_directory)

        elif choice == "6":
            settings_menu()

        elif choice == "7":
            console.print("\n🎵 Thanks for using Speech-to-Text! Goodbye!", style="bold green")
            break

        else:
            console.print("\n✗ Invalid choice. Please try again.", style="bold red")
            input("\nPress Enter to continue...")


def single_file_transcription(working_directory):
    """Handle single file transcription with interactive prompts."""
    console.print("\n--- Single File Transcription ---", style="bold blue")

    # Get input file
    default_file = working_directory / "audioclip.mp3"
    file_prompt = f"Audio file path [{default_file}]: "
    input_file = input(file_prompt).strip() or str(default_file)

    if not Path(input_file).exists():
        console.print(f"✗ File not found: {input_file}", style="bold red")
        input("\nPress Enter to continue...")
        return

    # Get output directory
    default_output = working_directory / "output"
    output_dir = input(f"Output directory [{default_output}]: ").strip() or str(default_output)

    # Get model
    models = ['tiny', 'base', 'small', 'medium', 'large', 'turbo']
    console.print(f"\nAvailable models: {', '.join(models)}", style="cyan")
    model = input("Model [medium]: ").strip() or "medium"

    # Get language
    languages = ['english', 'norwegian', 'french', 'spanish', 'german']
    console.print(f"Available languages: {', '.join(languages)}", style="cyan")
    language = input("Language [english]: ").strip() or "english"

    # Get temperature
    temperature = input("Temperature (0.0-1.0) [0.0]: ").strip() or "0.0"

    # Get file handling preference
    console.print("\nFile handling options:", style="cyan")
    console.print("1. Skip existing files (default)")
    console.print("2. Overwrite existing files")
    console.print("3. Backup existing files with auto-incremented names")
    file_handling = input("Choose option (1/2/3) [1]: ").strip() or "1"

    overwrite = file_handling == "2"
    backup = file_handling == "3"

    try:
        console.print(f"\n🎵 Starting transcription...", style="bold green")
        result = transcribe_and_save(
            input_file=input_file,
            output_dir=output_dir,
            model=model,
            language=language,
            temperature=float(temperature),
            overwrite=overwrite,
            backup=backup
        )
        console.print(f"✓ Transcription completed: {result}", style="bold green")

    except Exception as e:
        console.print(f"✗ Error during transcription: {e}", style="bold red")
        logger.error(f"Transcription error: {e}")

    input("\nPress Enter to continue...")


def batch_transcription(working_directory):
    """Handle batch transcription of multiple files."""
    console.print("\n--- Batch Transcription ---", style="bold blue")

    # Get input files
    files_input = input("Enter audio file paths (space-separated): ").strip()
    if not files_input:
        console.print("✗ No files specified", style="bold red")
        input("\nPress Enter to continue...")
        return

    try:
        input_files = shlex.split(files_input)
    except ValueError as e:
        console.print(f"✗ Error parsing file paths: {e}", style="bold red")
        input("\nPress Enter to continue...")
        return

    # Validate files exist
    valid_files = []
    for file_path in input_files:
        if Path(file_path).exists():
            valid_files.append(file_path)
        else:
            console.print(f"⚠️  File not found: {file_path}", style="yellow")

    if not valid_files:
        console.print("✗ No valid files found", style="bold red")
        input("\nPress Enter to continue...")
        return

    # Get common settings
    default_output = working_directory / "output"
    output_dir = input(f"Output directory [{default_output}]: ").strip() or str(default_output)
    model = input("Model [medium]: ").strip() or "medium"
    language = input("Language [english]: ").strip() or "english"
    temperature = input("Temperature [0.0]: ").strip() or "0.0"

    # Process files
    console.print(f"\n🎵 Processing {len(valid_files)} files...", style="bold green")

    with ThreadPoolExecutor(max_workers=2) as executor:
        futures = []
        for file_path in valid_files:
            future = executor.submit(
                transcribe_and_save,
                input_file=file_path,
                output_dir=output_dir,
                model=model,
                language=language,
                temperature=float(temperature)
            )
            futures.append((future, file_path))

        # Wait for completion
        for future, file_path in futures:
            try:
                result = future.result()
                console.print(f"✓ Completed: {Path(file_path).name}", style="green")
            except Exception as e:
                console.print(f"✗ Failed: {Path(file_path).name} - {e}", style="red")

    console.print("\n🎉 Batch transcription completed!", style="bold green")
    input("\nPress Enter to continue...")


def config_file_transcription(working_directory):
    """Handle transcription using configuration files."""
    console.print("\n--- Configuration File Transcription ---", style="bold blue")

    # List available config files
    config_files = list(working_directory.glob("*.yaml")) + list(working_directory.glob("*.yml"))

    if config_files:
        console.print("\nAvailable configuration files:", style="cyan")
        for i, config_file in enumerate(config_files, 1):
            console.print(f"  {i}. {config_file.name}")

        choice = input(f"\nSelect config file (1-{len(config_files)}) or enter custom path: ").strip()

        try:
            if choice.isdigit() and 1 <= int(choice) <= len(config_files):
                config_path = config_files[int(choice) - 1]
            else:
                config_path = Path(choice)
        except (ValueError, IndexError):
            config_path = Path(choice) if choice else None
    else:
        config_path = Path(input("Enter configuration file path: ").strip())

    if not config_path or not config_path.exists():
        console.print("✗ Configuration file not found", style="bold red")
        input("\nPress Enter to continue...")
        return

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # Extract configuration
        output_dir = config.get('output_dir', 'transcriptions')
        language = config.get('language', 'english')
        model = config.get('model', 'medium')
        temperature = config.get('temperature', 0.0)
        files_config = config.get('files', [])

        if not files_config:
            console.print("✗ No files specified in configuration", style="bold red")
            input("\nPress Enter to continue...")
            return

        console.print(f"\n🎵 Processing {len(files_config)} files from config...", style="bold green")

        # Process files with threading
        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = []
            for file_config in files_config:
                file_path = file_config.get('file_path')
                file_language = file_config.get('language', language)
                file_model = file_config.get('model', model)
                file_temperature = file_config.get('temperature', temperature)

                if not file_path:
                    console.print(f"⚠️  Skipping file with no path", style="yellow")
                    continue

                # Check if file is an audio/video file
                audio_extensions = {'.mp3', '.wav', '.m4a', '.flac', '.ogg', '.mp4', '.avi', '.mov', '.mkv', '.webm', '.aac', '.wma'}
                file_ext = Path(file_path).suffix.lower()
                if file_ext not in audio_extensions:
                    console.print(f"⚠️  Skipping non-audio file: {Path(file_path).name} ({file_ext})", style="yellow")
                    continue

                future = executor.submit(
                    transcribe_and_save,
                    input_file=file_path,
                    output_dir=output_dir,
                    model=file_model,
                    language=file_language,
                    temperature=file_temperature
                )
                futures.append((future, file_path))

            # Wait for completion
            for future, file_path in futures:
                try:
                    result = future.result()
                    console.print(f"✓ Completed: {Path(file_path).name}", style="green")
                except Exception as e:
                    console.print(f"✗ Failed: {Path(file_path).name} - {e}", style="red")

        console.print("\n🎉 Configuration-based transcription completed!", style="bold green")

    except Exception as e:
        console.print(f"✗ Error processing configuration: {e}", style="bold red")
        logger.error(f"Configuration error: {e}")

    input("\nPress Enter to continue...")


def download_models():
    """Download and manage Whisper models."""
    console.print("\n--- Whisper Model Management ---", style="bold blue")

    models = ['tiny', 'base', 'small', 'medium', 'large', 'turbo']
    console.print("\nAvailable models:", style="cyan")
    for i, model in enumerate(models, 1):
        console.print(f"  {i}. {model}")

    choice = input(f"\nSelect model to download (1-{len(models)}): ").strip()

    try:
        if choice.isdigit() and 1 <= int(choice) <= len(models):
            model = models[int(choice) - 1]
            console.print(f"\n🔄 Downloading {model} model...", style="bold yellow")

            # Load the model (this will download it if not present)
            whisper_model = whisper.load_model(model)
            console.print(f"✓ Model {model} downloaded and ready!", style="bold green")

        else:
            console.print("✗ Invalid selection", style="bold red")

    except Exception as e:
        console.print(f"✗ Error downloading model: {e}", style="bold red")
        logger.error(f"Model download error: {e}")

    input("\nPress Enter to continue...")


def view_history(working_directory):
    """View transcription history and outputs."""
    console.print("\n--- Transcription History ---", style="bold blue")

    output_dir = working_directory / "output"
    if not output_dir.exists():
        console.print("✗ No output directory found", style="bold red")
        input("\nPress Enter to continue...")
        return

    # Find transcription files
    txt_files = list(output_dir.glob("*.txt"))
    md_files = list(output_dir.glob("*.md"))
    all_files = sorted(txt_files + md_files, key=lambda x: x.stat().st_mtime, reverse=True)

    if not all_files:
        console.print("✗ No transcription files found", style="bold red")
        input("\nPress Enter to continue...")
        return

    console.print(f"\nFound {len(all_files)} transcription files:", style="cyan")
    for i, file_path in enumerate(all_files[:10], 1):  # Show last 10 files
        size = file_path.stat().st_size
        modified = datetime.fromtimestamp(file_path.stat().st_mtime).strftime("%Y-%m-%d %H:%M")
        console.print(f"  {i:2d}. {file_path.name} ({size} bytes, {modified})")

    if len(all_files) > 10:
        console.print(f"  ... and {len(all_files) - 10} more files")

    choice = input(f"\nSelect file to view (1-{min(10, len(all_files))}) or Enter to go back: ").strip()

    if choice.isdigit() and 1 <= int(choice) <= min(10, len(all_files)):
        file_path = all_files[int(choice) - 1]
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            console.print(f"\n--- {file_path.name} ---", style="bold cyan")
            console.print(content[:1000] + ("..." if len(content) > 1000 else ""))

        except Exception as e:
            console.print(f"✗ Error reading file: {e}", style="bold red")

    input("\nPress Enter to continue...")


def settings_menu():
    """Settings and preferences menu."""
    console.print("\n--- Settings & Preferences ---", style="bold blue")

    print("1. View current settings")
    print("2. Set default model")
    print("3. Set default language")
    print("4. Set default output directory")
    print("5. View system information")
    print("6. Back")

    choice = input("\n> ").strip()

    if choice == "1":
        console.print("\n--- Current Settings ---", style="cyan")
        console.print(f"Default model: medium")
        console.print(f"Default language: english")
        console.print(f"Default output: ./output")
        console.print(f"Python version: {sys.version}")

    elif choice == "5":
        console.print("\n--- System Information ---", style="cyan")
        console.print(f"Platform: {platform.system()} {platform.release()}")
        console.print(f"Python: {sys.version}")
        console.print(f"Working directory: {Path.cwd()}")

        # Check for dependencies
        try:
            import torch
            console.print(f"PyTorch: {torch.__version__}")
        except ImportError:
            console.print("PyTorch: Not installed")

        try:
            import whisper
            console.print("Whisper: Available")
        except ImportError:
            console.print("Whisper: Not installed")

    else:
        console.print("✗ Feature not implemented yet", style="yellow")

    input("\nPress Enter to continue...")


def display_header():
    """Display application header."""
    console.print("🎵 Speech-to-Text Transcription Tool", style="bold magenta")
    console.print("Powered by OpenAI Whisper", style="dim")


def main():
    args = parse_arguments()
    if args.debug:
        logger_config.verbosity = "verbose"
    logger_config.setup_logger()

    # Determine if we should use interactive CLI
    has_config = args.config is not None
    has_input_files = args.input_files and len(args.input_files) > 0
    has_prompt_flag = args.prompt

    # Use interactive CLI if:
    # 1. No arguments at all, OR
    # 2. No config file AND no input files AND no --prompt flag
    if (len(sys.argv) == 1 or
        (not has_config and not has_input_files and not has_prompt_flag)):
        interactive_session()
        return

    working_directory = Path.cwd()

    # ------------------------------------------------------------------
    # INTERACTIVE PROMPT: Ask if user wants to load a config file on the fly
    # (only if --prompt is specified AND they haven't already used --config).
    # ------------------------------------------------------------------
    if args.prompt and not args.config:
        load_config_answer = Prompt.ask(
            "Load a YAML/JSON configuration file? [y/n]",
            default="n"
        ).lower()

        if load_config_answer == 'y':
            config_path = Prompt.ask("Enter the path to your config file")
            if config_path and Path(config_path).is_file():
                config_data = load_config(config_path)
                if 'files' in config_data:
                    args.batch_mode = True
                    args.batch_files = config_data['files']
                    args.output_dir = config_data.get('output_dir', args.output_dir)
                    args.model = config_data.get('model', args.model)
                    args.language = config_data.get('language', args.language)
                    args.temperature = config_data.get('temperature', args.temperature)
                else:
                    args.input_files = config_data.get('input_files', args.input_files)
                    args.output_dir = config_data.get('output_dir', args.output_dir)
                    args.model = config_data.get('model', args.model)
                    args.language = config_data.get('language', args.language)
                    args.temperature = config_data.get('temperature', args.temperature)
            else:
                console.print("[bold yellow]Config path not found or invalid. Continuing without config.[/bold yellow]")

    # ------------------------------------------------------------------
    # If in batch mode, skip single-file prompts entirely.
    # ------------------------------------------------------------------
    if args.batch_mode:
        batch_tasks = args.batch_files
        output_dir = args.output_dir or (working_directory / "output")

        # -- SINGLE-THREAD EXECUTION (no ThreadPoolExecutor) --
        for item in batch_tasks:
            try:
                file_path = item['file_path']
                model = item.get('model', args.model)
                language = item.get('language', args.language)
                temperature = float(item.get('temperature', args.temperature))

                transcribe_and_save(
                    input_file=file_path,
                    output_dir=output_dir,
                    model=model,
                    language=language,
                    temperature=temperature,
                    overwrite=args.overwrite,
                    backup=args.backup
                )
            except Exception as e:
                console.print(f"An error occurred during batch transcription: {e}", style="bold red")
                logger.error(f"An error occurred during batch transcription: {e}")

        display_summary(args, "Batch transcription completed for all files")

    else:
        # --------------------------------------------------------------
        # SINGLE-FILE FLOW
        # --------------------------------------------------------------
        default_input_files = args.input_files if args.input_files else [working_directory / "audioclip.mp3"]
        default_output_dir = args.output_dir if args.output_dir else (working_directory / "output")
        default_model = args.model if args.model else "base"
        valid_languages = ['english', 'norwegian', 'french', 'spanish', 'german']
        default_language = args.language if args.language else "english"
        default_temperature = str(args.temperature) if args.temperature is not None else "0.0"

        while True:
            clear_console()

            if args.prompt:
                input_files_prompt = Prompt.ask(
                    "Input files (space-separated, use quotes if paths contain spaces or commas)",
                    default=" ".join(f'"{file}"' for file in default_input_files)
                )
                try:
                    input_files = shlex.split(input_files_prompt)
                except ValueError as e:
                    console.print(f"Error parsing input files: {e}", style="bold red")
                    logger.error(f"Error parsing input files: {e}")
                    continue

                args.input_files = resolve_paths(input_files, working_directory)
                args.output_dir = Prompt.ask("Output directory", default=str(default_output_dir))
                args.model = Prompt.ask(
                    "Model to use (tiny, base, small, medium, large, tiny.en, base.en, etc.)",
                    default=default_model
                )
                args.language = Prompt.ask(
                    "Language of the audio",
                    choices=valid_languages,
                    default=default_language
                )
                args.temperature = Prompt.ask("Temperature for transcription", default=default_temperature)
            else:
                # If user isn't prompted, just resolve pre-existing input files
                args.input_files = resolve_paths(args.input_files, working_directory)

            print(f"Resolved input file paths: {args.input_files}")

            # -- SINGLE-THREAD EXECUTION FOR SINGLE-FILE MODE --
            for input_file in args.input_files:
                try:
                    transcribe_and_save(
                        input_file=input_file,
                        output_dir=args.output_dir or str(default_output_dir),
                        model=args.model,
                        language=args.language,
                        temperature=float(args.temperature),
                        overwrite=args.overwrite,
                        backup=args.backup
                    )
                except Exception as e:
                    console.print(f"An error occurred during transcription: {e}", style="bold red")
                    logger.error(f"An error occurred during transcription: {e}")

            display_summary(args, "Transcription completed for all files")

            if not Confirm.ask("Do you want to transcribe more files?", default=False):
                break


if __name__ == "__main__":
    main()
