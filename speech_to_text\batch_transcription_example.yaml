# =============================================================================
# Speech-to-Text Batch Configuration Example
# =============================================================================
# This file demonstrates all available configuration options for batch processing.
# Copy this file and modify it for your specific transcription needs.

# =============================================================================
# Optional Settings
# =============================================================================
logging_level: "INFO"  # Options: DEBUG, INFO, WARNING, ERROR

# =============================================================================
# Global Configuration (Applied to all files unless overridden)
# =============================================================================
output_dir: "output"           # Directory where transcriptions will be saved
language: "english"            # Default language for all files
model: "medium"                # Default Whisper model to use
temperature: 0.0               # Default temperature (0.0 = deterministic, higher = more creative)

# Available languages: english, norwegian, french, spanish, german
# Available models: tiny, base, small, medium, large, turbo
#   - tiny:   Fastest, least accurate (~39 MB)
#   - base:   Good balance (~74 MB)  
#   - small:  Better accuracy (~244 MB)
#   - medium: High accuracy (~769 MB)
#   - large:  Best accuracy (~1550 MB)
#   - turbo:  Fast and accurate (~809 MB)

# =============================================================================
# File List - Transcription Tasks
# =============================================================================
files:
  # Example 1: Basic file with global settings
  - file_path: "samples/audio1.mp3"
    # Uses global settings: english, medium model, temperature 0.0

  # Example 2: File with custom model override
  - file_path: "samples/audio2.wav"
    model: "large"
    # Uses global language (english) and temperature (0.0)

  # Example 3: File with multiple overrides
  - file_path: "samples/norwegian_audio.mp4"
    language: "norwegian"
    model: "large"
    temperature: 0.1

  # Example 4: Different language and model
  - file_path: "samples/french_interview.m4a"
    language: "french"
    model: "medium"
    temperature: 0.0

  # Example 5: High-quality transcription
  - file_path: "samples/important_meeting.wav"
    model: "large"
    temperature: 0.0
    # Uses global language (english)

  # Example 6: Creative transcription (higher temperature)
  - file_path: "samples/creative_content.mp3"
    model: "medium"
    temperature: 0.3
    # Higher temperature for more creative/varied transcription

# =============================================================================
# Usage Instructions
# =============================================================================
# 1. Copy this file and rename it (e.g., my_batch_config.yaml)
# 2. Update the file_path entries to point to your actual audio files
# 3. Adjust global settings and per-file overrides as needed
# 4. Run the batch transcription:
#
#    Command line:
#    uv run python src\main.py --config my_batch_config.yaml
#
#    Interactive CLI:
#    uv run python src\main.py
#    Then select option 3: "Use configuration file"
#
# =============================================================================
# File Path Examples
# =============================================================================
# Absolute paths:
#   - file_path: "C:/Users/<USER>/Documents/audio.mp3"
#   - file_path: "/home/<USER>/recordings/meeting.wav"
#
# Relative paths (from project directory):
#   - file_path: "samples/audio.mp3"
#   - file_path: "../recordings/interview.wav"
#
# Network paths:
#   - file_path: "\\server\share\audio\file.mp3"
#
# =============================================================================
# Supported Audio Formats
# =============================================================================
# - MP3, WAV, M4A, FLAC, OGG
# - MP4, AVI, MOV (audio will be extracted)
# - Most common audio/video formats supported by FFmpeg
#
# =============================================================================
# Performance Tips
# =============================================================================
# - Use "medium" model for good balance of speed and accuracy
# - Use "large" model only for critical transcriptions
# - Keep temperature at 0.0 for consistent, deterministic results
# - Process files in batches of 10-20 for optimal performance
# - Ensure sufficient disk space for output files
#
# =============================================================================
