#!/usr/bin/env python3
"""
Test the prompt mode with a dummy file to see the actual prompts.
"""

import subprocess
import sys
import os

def test_prompt_mode():
    """Test prompt mode with CLI arguments."""
    
    # Create a dummy audio file for testing
    dummy_file = "dummy_audio.mp3"
    with open(dummy_file, 'w') as f:
        f.write("dummy content")
    
    try:
        print("=== Testing Prompt Mode with CLI Arguments ===")
        print("Command: uv run python src\\main.py dummy_audio.mp3 --prompt --model large --language norwegian")
        print("\nThis should show prompts with 'large' and 'norwegian' as defaults...")
        print("(Press Ctrl+C to exit when you see the prompts)")
        
        # Run the command
        result = subprocess.run([
            "uv", "run", "python", "src\\main.py", 
            dummy_file, "--prompt", "--model", "large", "--language", "norwegian"
        ], cwd="speech_to_text", capture_output=True, text=True, timeout=5)
        
        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")
        
    except subprocess.TimeoutExpired:
        print("Command timed out (expected - waiting for user input)")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        if os.path.exists(dummy_file):
            os.remove(dummy_file)

if __name__ == "__main__":
    test_prompt_mode()
