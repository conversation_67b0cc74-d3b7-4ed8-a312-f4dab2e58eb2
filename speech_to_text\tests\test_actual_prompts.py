#!/usr/bin/env python3
"""
Test the actual Rich prompts with CLI defaults.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from rich.prompt import Prompt

def test_rich_prompts():
    """Test Rich prompts with CLI-derived defaults."""
    
    # Simulate CLI arguments
    cli_model = 'large'
    cli_language = 'norwegian'
    cli_temperature = '0.5'
    
    print("=== Testing Rich Prompts with CLI Defaults ===")
    print(f"CLI arguments: --model {cli_model} --language {cli_language} --temperature {cli_temperature}")
    print("\nThe prompts should show these as defaults:")
    
    # Test what the prompts would look like
    valid_languages = ['english', 'norwegian', 'french', 'spanish', 'german']
    
    print(f"\n1. Model prompt should show: 'Model to use (...) ({cli_model}):'")
    print(f"2. Language prompt should show: 'Language of the audio ({cli_language}):'")
    print(f"3. Temperature prompt should show: 'Temperature for transcription ({cli_temperature}):'")
    
    print(f"\nActual Rich prompt behavior:")
    print(f"Prompt.ask('Model', default='{cli_model}') -> shows ({cli_model})")
    print(f"Prompt.ask('Language', choices={valid_languages}, default='{cli_language}') -> shows ({cli_language})")
    print(f"Prompt.ask('Temperature', default='{cli_temperature}') -> shows ({cli_temperature})")

if __name__ == "__main__":
    test_rich_prompts()
