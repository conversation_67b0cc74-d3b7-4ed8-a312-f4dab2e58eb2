#!/usr/bin/env python3
"""
Validation script to test CLI argument defaults in interactive session.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import DefaultsResolver

def test_defaults_resolver():
    """Test the DefaultsResolver functionality."""
    print("=== Testing DefaultsResolver ===")
    
    # Test 1: No CLI args
    defaults = DefaultsResolver()
    print("\n1. Without CLI args:")
    print(f"   Model: '{defaults.get('model')}'")
    print(f"   Language: '{defaults.get('language')}'")
    print(f"   Temperature: '{defaults.get('temperature')}'")
    print(f"   Output Dir: '{defaults.get('output_dir')}'")
    
    # Test 2: With CLI args
    class MockArgs:
        def __init__(self):
            self.model = 'large'
            self.language = 'norwegian'
            self.temperature = 0.5
            self.output_dir = '/custom/output'
    
    args = MockArgs()
    defaults.set_cli_args(args)
    
    print("\n2. With CLI args (large, norwegian, 0.5, /custom/output):")
    print(f"   Model: '{defaults.get('model')}'")
    print(f"   Language: '{defaults.get('language')}'")
    print(f"   Temperature: '{defaults.get('temperature')}'")
    print(f"   Output Dir: '{defaults.get('output_dir')}'")
    
    # Test 3: Partial CLI args (some None values)
    class PartialArgs:
        def __init__(self):
            self.model = 'tiny'
            self.language = None  # Should fall back to default
            self.temperature = None  # Should fall back to default
            self.output_dir = None  # Should fall back to default
    
    partial_args = PartialArgs()
    defaults.set_cli_args(partial_args)
    
    print("\n3. With partial CLI args (only model='tiny', others None):")
    print(f"   Model: '{defaults.get('model')}'")
    print(f"   Language: '{defaults.get('language')}'")
    print(f"   Temperature: '{defaults.get('temperature')}'")
    print(f"   Output Dir: '{defaults.get('output_dir')}'")
    
    print("\n=== All tests completed ===")

if __name__ == "__main__":
    test_defaults_resolver()
