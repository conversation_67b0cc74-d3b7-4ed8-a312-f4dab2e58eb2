#!/usr/bin/env python3
"""
Debug script to check argument parsing.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Mock sys.argv to simulate the command line
sys.argv = ['main.py', '--prompt', '--model', 'large', '--language', 'norwegian']

from main import parse_arguments

def debug_argument_parsing():
    """Debug the argument parsing to see what's happening."""
    
    print("=== Debugging Argument Parsing ===")
    print(f"Simulated command: {' '.join(sys.argv)}")
    
    args = parse_arguments()
    
    print(f"\nParsed arguments:")
    print(f"  args.model: '{args.model}'")
    print(f"  args.language: '{args.language}'")
    print(f"  args.temperature: {args.temperature}")
    print(f"  args.prompt: {args.prompt}")
    print(f"  args.input_files: {args.input_files}")
    
    # Test the default resolution logic
    default_model = args.model if args.model else "medium"
    default_language = args.language if args.language else "english"
    default_temperature = str(args.temperature) if args.temperature is not None else "0.0"
    
    print(f"\nDefault resolution:")
    print(f"  default_model: '{default_model}'")
    print(f"  default_language: '{default_language}'")
    print(f"  default_temperature: '{default_temperature}'")
    
    # Check the conditions for prompt mode
    has_config = args.config is not None
    has_input_files = args.input_files and len(args.input_files) > 0
    has_prompt_flag = args.prompt
    
    print(f"\nMode determination:")
    print(f"  has_config: {has_config}")
    print(f"  has_input_files: {has_input_files}")
    print(f"  has_prompt_flag: {has_prompt_flag}")
    
    # Check if it would trigger interactive session
    would_trigger_interactive = (len(sys.argv) == 1 or
        (not has_config and not has_input_files and not has_prompt_flag))
    
    print(f"  would_trigger_interactive: {would_trigger_interactive}")
    print(f"  should_use_prompt_mode: {has_prompt_flag and not would_trigger_interactive}")

if __name__ == "__main__":
    debug_argument_parsing()
